2025-08-02 11:10:07.327 | INFO     | monitor:test_notifications:166 - Testing notification system...
2025-08-02 11:10:07.328 | INFO     | notifications:send_notification:30 - Sending notification: This is a test notification from your EV Station Monitor
2025-08-02 11:10:07.405 | INFO     | notifications:_send_desktop_notification:87 - Desktop notification sent successfully
2025-08-02 11:10:07.405 | INFO     | monitor:test_notifications:169 - Test notification sent successfully
2025-08-02 11:10:22.594 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:10:22.595 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:10:23.252 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000284B1EFAF70>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:10:23.257 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000284B1F07310>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:10:24.263 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:10:24.559 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000284B3268A90>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:10:24.561 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000284B3268E20>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:13:02.273 | INFO     | __main__:cmd_start:28 - Starting EV charging station monitor
2025-08-02 11:13:02.275 | INFO     | monitor:start_monitoring:31 - Starting EV charging station monitor
2025-08-02 11:13:02.275 | INFO     | monitor:start_monitoring:32 - Monitoring stations: ['HitTown-1', 'HitTown-2']
2025-08-02 11:13:02.276 | INFO     | monitor:start_monitoring:33 - Check interval: 5 minutes
2025-08-02 11:13:02.282 | INFO     | notifications:send_startup_notification:193 - Startup notification sent
2025-08-02 11:13:02.283 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:13:02.284 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:13:02.285 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:13:02.914 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000020D359E45E0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:13:02.917 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000020D359E49D0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:13:03.921 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:13:04.214 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000020D35B47160>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:13:04.217 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000020D35B474F0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:13:05.228 | INFO     | monitor:_process_station_update:83 - First check for HitTown-1: unknown
2025-08-02 11:13:05.229 | INFO     | monitor:_process_station_update:83 - First check for HitTown-2: unknown
2025-08-02 11:13:05.231 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:13:41.804 | INFO     | monitor:start_monitoring:46 - Monitoring stopped by user
2025-08-02 11:13:41.805 | INFO     | monitor:start_monitoring:51 - Monitoring stopped
2025-08-02 11:13:44.871 | INFO     | __main__:cmd_start:28 - Starting EV charging station monitor
2025-08-02 11:13:44.873 | INFO     | monitor:_load_previous_states:136 - Loaded previous states for 2 stations
2025-08-02 11:13:44.873 | INFO     | monitor:start_monitoring:31 - Starting EV charging station monitor
2025-08-02 11:13:44.874 | INFO     | monitor:start_monitoring:32 - Monitoring stations: ['HitTown-1', 'HitTown-2']
2025-08-02 11:13:44.875 | INFO     | monitor:start_monitoring:33 - Check interval: 1 minutes
2025-08-02 11:13:44.881 | INFO     | notifications:send_startup_notification:193 - Startup notification sent
2025-08-02 11:13:44.881 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:13:44.882 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:13:44.882 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:13:45.506 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000017110C3C910>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:13:45.510 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000017110C3CD00>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:13:46.513 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:13:46.843 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000017110DC84C0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:13:46.846 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000017110DC8850>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:13:47.853 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:14:48.312 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:14:48.313 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:14:48.313 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:14:48.854 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000017110AAE790>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:14:48.858 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000017111EAE2B0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:14:49.868 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:14:50.165 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000017111EAE490>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:14:50.167 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000017111F2D2E0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:14:51.168 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:15:51.662 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:15:51.663 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:15:51.663 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:15:52.285 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000017111EAE9A0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:15:52.288 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000017111F2D940>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:15:53.296 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:15:53.519 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000017111F7CA00>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:15:53.521 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000017111F7CE20>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:15:54.531 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:16:52.780 | INFO     | monitor:start_monitoring:46 - Monitoring stopped by user
2025-08-02 11:16:52.782 | INFO     | monitor:start_monitoring:51 - Monitoring stopped
2025-08-02 11:19:49.496 | INFO     | __main__:cmd_start:28 - Starting EV charging station monitor
2025-08-02 11:19:49.504 | INFO     | monitor:_load_previous_states:136 - Loaded previous states for 2 stations
2025-08-02 11:19:49.504 | INFO     | monitor:start_monitoring:31 - Starting EV charging station monitor
2025-08-02 11:19:49.504 | INFO     | monitor:start_monitoring:32 - Monitoring stations: ['HitTown-1', 'HitTown-2']
2025-08-02 11:19:49.505 | INFO     | monitor:start_monitoring:33 - Check interval: 1 minutes
2025-08-02 11:19:49.566 | INFO     | notifications:send_startup_notification:193 - Startup notification sent
2025-08-02 11:19:49.567 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:19:49.568 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:19:49.568 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:19:50.097 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC02261C0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:19:50.099 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0226760>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:19:51.099 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:19:51.287 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC022B160>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:19:51.288 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC022B3D0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:19:52.292 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:20:52.747 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:20:52.748 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:20:52.748 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:20:53.174 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC03E6850>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:20:53.178 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC03E6AC0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:20:54.189 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:20:54.401 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0669880>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:20:54.403 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0669AF0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:20:55.415 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:21:55.889 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:21:55.890 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:21:55.891 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:21:56.394 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0669970>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:21:56.396 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0673790>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:21:57.411 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:21:57.626 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC068D490>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:21:57.629 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC068D880>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:21:58.633 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:22:59.203 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:22:59.204 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:22:59.205 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:22:59.842 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0356460>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:22:59.845 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC03567F0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:23:00.848 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:23:01.062 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC07B1070>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:23:01.065 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC07B1400>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:23:02.074 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:24:02.545 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:24:02.546 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:24:02.547 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:24:02.969 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC03B2040>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:24:02.973 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC03B23D0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:24:03.980 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:24:04.192 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC088BEE0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:24:04.195 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0896100>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:24:05.204 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:25:05.647 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:25:05.647 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:25:05.648 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:25:06.542 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC096F7F0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:25:06.546 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC096FB80>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:25:07.562 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:25:07.879 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC09B2820>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:25:07.882 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC09B2BB0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:25:08.889 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:26:09.384 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:26:09.385 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:26:09.386 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:26:09.977 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0A563D0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:26:09.981 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0A56790>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:26:10.990 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:26:11.286 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0B3A040>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:26:11.289 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0B3A2B0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:26:12.296 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:27:12.743 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:27:12.744 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:27:12.745 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:27:13.347 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0ACC070>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:27:13.350 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0ACC3A0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:27:14.351 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:27:14.671 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0C14EB0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:27:14.674 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0C200D0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:27:15.678 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:28:16.128 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:28:16.129 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:28:16.130 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:28:16.724 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0CF8790>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:28:16.728 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0CF8A00>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:28:17.729 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:28:18.071 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BABF536130>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:28:18.074 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BABF536640>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:28:19.081 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:29:19.555 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:29:19.556 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:29:19.557 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:29:20.143 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0CF1C70>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:29:20.145 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0CE7250>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:29:21.158 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:29:21.485 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC02AB970>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:29:21.488 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC02ABD00>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:29:22.500 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:30:23.011 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:30:23.012 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:30:23.013 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:30:23.649 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC02CE940>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:30:23.653 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC02CECD0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:30:24.665 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:30:24.885 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC024B550>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:30:24.889 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC024B8E0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:30:25.898 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:31:26.376 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:31:26.377 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:31:26.378 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:31:26.800 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC03B50D0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:31:26.803 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC03B5490>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:31:27.819 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:31:28.041 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0BDD130>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:31:28.044 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0BDD4C0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:31:29.053 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:32:29.572 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:32:29.573 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:32:29.574 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:32:30.096 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC086DDC0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:32:30.100 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0875160>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:32:31.115 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:32:31.335 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0B9C880>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:32:31.338 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0B9CC40>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:32:32.351 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:33:32.836 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:33:32.837 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:33:32.838 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:33:33.264 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC032B880>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:33:33.268 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC032BAF0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:33:34.283 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:33:34.521 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC036D490>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:33:34.524 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC036D700>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:33:35.531 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:34:36.025 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:34:36.026 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:34:36.027 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:34:36.450 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC063D070>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:34:36.454 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC063D400>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:34:37.462 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:34:37.668 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0C990A0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:34:37.670 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0C99430>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:34:38.672 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:35:39.221 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:35:39.222 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:35:39.223 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:35:40.046 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0794D30>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:35:40.050 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC07A0100>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:35:41.052 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:35:41.264 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0A39820>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:35:41.267 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0A39BB0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:35:42.275 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:36:42.775 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:36:42.776 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:36:42.777 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:36:43.202 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC09C17F0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:36:43.206 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC09C1B80>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:36:44.207 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:36:44.455 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0C996A0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:36:44.458 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0C99250>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:36:45.469 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:37:46.001 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:37:46.002 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:37:46.003 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:37:46.656 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0C99D00>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:37:46.659 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0786250>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:37:47.667 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:37:47.884 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC032BE50>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:37:47.887 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0C99E20>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:37:48.903 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:38:49.413 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:38:49.414 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:38:49.415 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:38:49.832 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC09E4970>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:38:49.836 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC09E4D00>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:38:50.845 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:38:51.059 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0BFE580>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:38:51.062 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0BFE910>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:38:52.073 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:39:52.561 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:39:52.562 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:39:52.563 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:39:53.165 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0635550>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:39:53.169 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC06358E0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:39:54.178 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:39:54.390 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC07FB190>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:39:54.393 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC07FB520>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:39:55.407 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:40:55.945 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:40:55.946 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:40:55.946 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:40:56.690 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC07FA130>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:40:56.692 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC02771F0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:40:57.696 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:40:57.928 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0277730>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:40:57.931 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC037C220>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:40:58.937 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:41:59.436 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:41:59.437 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:41:59.438 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:41:59.972 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC07478E0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:41:59.975 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0747C70>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:42:00.991 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:42:01.195 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0759040>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:42:01.199 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0CC1C10>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:42:02.210 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:43:02.736 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:43:02.737 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:43:02.738 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:43:03.172 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC084C970>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:43:03.177 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC084C880>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:43:04.178 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:43:04.404 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0A46130>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:43:04.407 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0A464C0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:43:05.408 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:44:06.002 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:44:06.003 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:44:06.004 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:44:06.370 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0887580>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:44:06.374 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0887910>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:44:07.388 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:44:07.609 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0712DC0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:44:07.612 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC07161C0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:44:08.618 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:45:09.215 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:45:09.216 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:45:09.217 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:45:09.835 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC03EA880>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:45:09.838 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC03EAC10>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:45:10.839 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:45:11.183 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0DDDF40>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:45:11.186 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC0DEA1C0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:45:12.202 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
2025-08-02 11:46:12.788 | INFO     | monitor:_check_stations:59 - Checking station availability...
2025-08-02 11:46:12.789 | INFO     | station_checker:check_all_stations:191 - Checking 2 stations
2025-08-02 11:46:12.789 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-1
2025-08-02 11:46:13.229 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BABF535730>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:46:13.231 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BABF535B80>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:46:14.231 | INFO     | station_checker:check_station_availability:29 - Checking availability for station: HitTown-2
2025-08-02 11:46:14.429 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC032B9D0>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:46:14.431 | DEBUG    | station_checker:_check_via_mobile_api:138 - API endpoint https://api.ecoboxsarj.com/v1/stations failed: HTTPSConnectionPool(host='api.ecoboxsarj.com', port=443): Max retries exceeded with url: /v1/stations (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002BAC032BA60>: Failed to resolve 'api.ecoboxsarj.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-02 11:46:15.447 | INFO     | monitor:_check_stations:75 - Station check completed. Status: 2 unknown
