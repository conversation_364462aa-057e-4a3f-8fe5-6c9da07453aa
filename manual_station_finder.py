"""Manual methods to find station information."""

import requests
import json
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def try_browser_automation():
    """Use browser automation to access the map and find station data."""
    print("🌐 Trying browser automation to access Ecobox map...")
    
    # Setup Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # Run in background
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        
        # Try to access the map
        map_urls = [
            "http://harita.ecoboxsarj.com",
            "https://www.ecoboxsarj.com",
        ]
        
        for url in map_urls:
            try:
                print(f"Accessing: {url}")
                driver.get(url)
                time.sleep(5)  # Wait for page to load
                
                # Look for station data in page source
                page_source = driver.page_source
                
                # Check if HitTown is mentioned
                if "hittown" in page_source.lower():
                    print("✅ Found HitTown reference in page!")
                    
                # Look for JavaScript variables that might contain station data
                script_elements = driver.find_elements(By.TAG_NAME, "script")
                for script in script_elements:
                    script_content = script.get_attribute("innerHTML")
                    if script_content and ("station" in script_content.lower() or "istasyon" in script_content.lower()):
                        print(f"Found potential station data in script: {script_content[:200]}...")
                
                # Check network requests (this would require more advanced setup)
                logs = driver.get_log('performance')
                for log in logs:
                    message = json.loads(log['message'])
                    if message['message']['method'] == 'Network.responseReceived':
                        url_req = message['message']['params']['response']['url']
                        if 'api' in url_req or 'station' in url_req:
                            print(f"Found API request: {url_req}")
                            
            except Exception as e:
                print(f"Error accessing {url}: {e}")
                
        driver.quit()
        
    except Exception as e:
        print(f"Browser automation failed: {e}")
        print("Note: You need to install ChromeDriver for this to work")

def manual_network_inspection_guide():
    """Provide a guide for manual network inspection."""
    print("\n🔍 Manual Network Inspection Guide")
    print("=" * 50)
    
    print("""
📱 Method 1: Mobile App Network Monitoring

1. Install the Ecobox mobile app on your phone
2. Install a network monitoring app like:
   - Charles Proxy (iOS/Android)
   - HTTP Catcher (Android)
   - Proxyman (iOS)

3. Configure your phone to use the proxy
4. Open the Ecobox app and navigate to the station map
5. Look for API calls that contain station data
6. Note the API endpoints and request format

🌐 Method 2: Browser Developer Tools

1. Open Chrome/Firefox
2. Go to http://harita.ecoboxsarj.com
3. Open Developer Tools (F12)
4. Go to Network tab
5. Refresh the page
6. Look for XHR/Fetch requests that might contain station data
7. Check the Response tab for JSON data

🔧 Method 3: Browser Extension

1. Install a browser extension like:
   - Postman Interceptor
   - HTTP Request Interceptor
2. Visit the Ecobox website/map
3. Monitor all network requests
4. Look for patterns in the API calls

📊 What to Look For:

- API endpoints containing:
  * /api/stations
  * /stations
  * /charging-points
  * /locations
  * /map/data
  
- JSON responses with station information:
  * Station names (look for "HitTown")
  * Coordinates (latitude/longitude)
  * Status information (available/occupied)
  * Station IDs

🎯 Specific Steps for HitTown Stations:

1. In the mobile app, search for "HitTown"
2. Navigate to the HitTown area on the map
3. Monitor network requests when the map loads stations
4. Look for API calls that return station data for that area
5. Note the request URL, headers, and response format
""")

def create_api_test_script():
    """Create a script to test discovered API endpoints."""
    
    test_script = '''"""
Test script for discovered Ecobox API endpoints.
Fill in the discovered endpoints and run this script.
"""

import requests
import json

# Fill these in based on your discoveries
DISCOVERED_ENDPOINTS = [
    # Example: "https://api.ecoboxsarj.com/v1/stations"
    # Add discovered endpoints here
]

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Accept': 'application/json',
    # Add any discovered headers here
}

def test_endpoint(url):
    """Test a discovered endpoint."""
    try:
        response = requests.get(url, headers=HEADERS, timeout=10)
        print(f"\\n🔗 Testing: {url}")
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ Success! Data type: {type(data)}")
                
                # Look for HitTown stations
                if isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict):
                            name = item.get('name', '').lower()
                            if 'hittown' in name:
                                print(f"🎯 Found HitTown station: {item}")
                elif isinstance(data, dict):
                    print(f"Response keys: {list(data.keys())}")
                    
                # Save response for analysis
                with open(f'api_response_{url.split("/")[-1]}.json', 'w') as f:
                    json.dump(data, f, indent=2)
                    
            except json.JSONDecodeError:
                print(f"⚠️  Response is not JSON: {response.text[:200]}...")
        else:
            print(f"❌ Failed with status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    if not DISCOVERED_ENDPOINTS:
        print("❌ No endpoints to test. Please add discovered endpoints to DISCOVERED_ENDPOINTS list.")
        return
        
    for endpoint in DISCOVERED_ENDPOINTS:
        test_endpoint(endpoint)

if __name__ == "__main__":
    main()
'''
    
    with open('test_discovered_apis.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("📝 Created test_discovered_apis.py")
    print("   Edit this file to add any API endpoints you discover")

def main():
    print("🔍 Station Data Discovery Methods")
    print("=" * 50)
    
    # Try browser automation if possible
    try:
        try_browser_automation()
    except:
        print("⚠️  Browser automation not available (ChromeDriver not installed)")
    
    # Provide manual inspection guide
    manual_network_inspection_guide()
    
    # Create API test script
    create_api_test_script()
    
    print("\n🎯 Recommended Next Steps:")
    print("1. Use the mobile app with network monitoring")
    print("2. Check browser developer tools on the map page")
    print("3. Look for API endpoints that return station data")
    print("4. Test discovered endpoints with test_discovered_apis.py")

if __name__ == "__main__":
    main()
