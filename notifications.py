"""Notification system for EV charging station availability."""

import smtplib
import platform
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON><PERSON>art
from typing import List, Optional

from loguru import logger

try:
    from plyer import notification
    PLYER_AVAILABLE = True
except ImportError:
    PLYER_AVAILABLE = False
    logger.warning("plyer not available, desktop notifications disabled")

from models import NotificationEvent, StationStatus
from config import config

class NotificationManager:
    """Manages different types of notifications."""
    
    def __init__(self):
        self.last_notifications = {}  # Track last notification time per station
        
    def send_notification(self, event: NotificationEvent) -> bool:
        """Send notification for a station status change event."""
        logger.info(f"Sending notification: {event.message}")
        
        success = True
        
        # Send desktop notification
        if config.notifications.desktop_enabled:
            success &= self._send_desktop_notification(event)
            
        # Send email notification
        if config.notifications.email_enabled:
            success &= self._send_email_notification(event)
            
        # Update last notification time
        self.last_notifications[event.station_name] = datetime.now()
        
        return success
    
    def should_notify(self, station_name: str, new_status: StationStatus) -> bool:
        """Check if we should send a notification for this status change."""
        # Only notify when station becomes available
        if new_status != StationStatus.AVAILABLE:
            return False
            
        # Check if we've already notified recently (avoid spam)
        last_notification = self.last_notifications.get(station_name)
        if last_notification:
            time_since_last = datetime.now() - last_notification
            # Don't notify more than once every 10 minutes
            if time_since_last.total_seconds() < 600:
                return False
                
        return True
    
    def _send_desktop_notification(self, event: NotificationEvent) -> bool:
        """Send desktop notification."""
        if not PLYER_AVAILABLE:
            logger.warning("Desktop notifications not available")
            return False
            
        try:
            title = "🔋 EV Charging Station Available!"
            message = f"{event.station_name} is now {event.new_status.value.replace('_', ' ').title()}"
            
            # Determine icon based on status
            if event.new_status == StationStatus.AVAILABLE:
                app_icon = None  # Use default icon
            else:
                app_icon = None
                
            notification.notify(
                title=title,
                message=message,
                app_name="EV Station Monitor",
                app_icon=app_icon,
                timeout=10,
            )
            
            logger.info("Desktop notification sent successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send desktop notification: {str(e)}")
            return False
    
    def _send_email_notification(self, event: NotificationEvent) -> bool:
        """Send email notification."""
        if not all([config.notifications.email_from, 
                   config.notifications.email_to, 
                   config.notifications.email_password]):
            logger.warning("Email configuration incomplete")
            return False
            
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = config.notifications.email_from
            msg['To'] = config.notifications.email_to
            msg['Subject'] = f"🔋 EV Station Available: {event.station_name}"
            
            # Email body
            body = self._create_email_body(event)
            msg.attach(MIMEText(body, 'html'))
            
            # Send email
            server = smtplib.SMTP(config.notifications.email_smtp_server, 
                                config.notifications.email_smtp_port)
            server.starttls()
            server.login(config.notifications.email_from, 
                        config.notifications.email_password)
            
            text = msg.as_string()
            server.sendmail(config.notifications.email_from, 
                          config.notifications.email_to, text)
            server.quit()
            
            logger.info("Email notification sent successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email notification: {str(e)}")
            return False
    
    def _create_email_body(self, event: NotificationEvent) -> str:
        """Create HTML email body."""
        status_color = {
            StationStatus.AVAILABLE: "#28a745",
            StationStatus.OCCUPIED: "#dc3545", 
            StationStatus.OUT_OF_ORDER: "#ffc107",
            StationStatus.UNKNOWN: "#6c757d"
        }.get(event.new_status, "#6c757d")
        
        return f"""
        <html>
        <body style="font-family: Arial, sans-serif; margin: 20px;">
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px;">
                <h2 style="color: #333; margin-top: 0;">🔋 EV Charging Station Update</h2>
                
                <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0;">
                    <h3 style="color: {status_color}; margin-top: 0;">
                        {event.station_name}
                    </h3>
                    <p style="font-size: 16px; margin: 10px 0;">
                        Status changed from <strong>{event.old_status.value.replace('_', ' ').title()}</strong> 
                        to <strong style="color: {status_color};">
                        {event.new_status.value.replace('_', ' ').title()}</strong>
                    </p>
                    <p style="color: #666; font-size: 14px;">
                        Time: {event.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
                    </p>
                </div>
                
                <div style="margin-top: 20px; padding: 10px; background-color: #e9ecef; border-radius: 5px;">
                    <p style="margin: 0; font-size: 12px; color: #666;">
                        This notification was sent by your EV Charging Station Monitor bot.
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def send_test_notification(self) -> bool:
        """Send a test notification to verify the system is working."""
        test_event = NotificationEvent(
            station_name="Test Station",
            old_status=StationStatus.OCCUPIED,
            new_status=StationStatus.AVAILABLE,
            message="This is a test notification from your EV Station Monitor"
        )
        
        return self.send_notification(test_event)
    
    def send_startup_notification(self) -> bool:
        """Send notification when the bot starts monitoring."""
        try:
            if config.notifications.desktop_enabled and PLYER_AVAILABLE:
                notification.notify(
                    title="🤖 EV Station Monitor Started",
                    message=f"Now monitoring {len(config.stations)} stations",
                    app_name="EV Station Monitor",
                    timeout=5,
                )
                
            logger.info("Startup notification sent")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send startup notification: {str(e)}")
            return False
