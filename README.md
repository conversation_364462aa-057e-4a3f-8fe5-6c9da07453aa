# 🔋 EV Charging Station Monitor

A Python bot that monitors Ecobox charging station availability in Turkey and sends notifications when your preferred stations become available.

## 🎯 Features

- **Real-time Monitoring**: Continuously checks HitTown-1 and HitTown-2 charging stations
- **Smart Notifications**: Desktop and email alerts when stations become available
- **Configurable Intervals**: Set custom check frequencies (default: 5 minutes)
- **State Persistence**: Remembers previous states to avoid duplicate notifications
- **Multiple Data Sources**: Attempts to gather data from various Ecobox endpoints
- **Easy Setup**: Simple command-line interface for configuration and operation

## 🚀 Quick Start

### 1. Install Dependencies

```bash
# Install Python dependencies
python cli.py install

# Or manually:
pip install -r requirements.txt
```

### 2. Initial Setup

```bash
python cli.py setup
```

This creates a `.env` file where you can configure your preferences.

### 3. Configure Notifications (Optional)

Edit the `.env` file to enable email notifications:

```env
# Email Notifications
EMAIL_ENABLED=true
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>
EMAIL_PASSWORD=your-app-password
```

### 4. Test the System

```bash
# Test notifications
python cli.py test

# Check current station status
python cli.py check
```

### 5. Start Monitoring

```bash
python cli.py start
```

## 📋 Available Commands

| Command | Description |
|---------|-------------|
| `python cli.py setup` | Initial configuration setup |
| `python cli.py install` | Install required dependencies |
| `python cli.py start` | Start continuous monitoring |
| `python cli.py check` | Check current station status once |
| `python cli.py test` | Test notification system |
| `python cli.py config` | Show current configuration |

## ⚙️ Configuration

### Environment Variables

Create a `.env` file with these settings:

```env
# Email Notifications (optional)
EMAIL_ENABLED=false
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587

# Monitoring Settings
CHECK_INTERVAL_MINUTES=5
MAX_RETRIES=3
TIMEOUT_SECONDS=30

# Desktop Notifications
DESKTOP_NOTIFICATIONS=true
SOUND_NOTIFICATIONS=true
```

### Monitored Stations

The bot is pre-configured to monitor:
- **HitTown-1**: Ecobox charging station at HitTown location
- **HitTown-2**: Second Ecobox charging station at HitTown location

## 🔔 Notification Types

### Desktop Notifications
- Instant pop-up notifications when stations become available
- Works on Windows, macOS, and Linux
- Automatically enabled by default

### Email Notifications
- HTML-formatted emails with station status changes
- Configurable SMTP settings
- Supports Gmail and other email providers

## 📊 How It Works

1. **Data Collection**: The bot attempts to gather station data from multiple sources:
   - Ecobox map API endpoints
   - Website scraping
   - Mobile app API calls

2. **Status Detection**: Identifies station availability states:
   - ✅ Available
   - 🔴 Occupied
   - ⚠️ Out of Order
   - ❓ Unknown

3. **Smart Notifications**: Only notifies when:
   - Station becomes available (not when it becomes occupied)
   - At least 10 minutes have passed since the last notification
   - Status actually changed from the previous check

4. **State Persistence**: Saves station states to `station_states.json` to:
   - Remember status between restarts
   - Avoid duplicate notifications
   - Track status change history

## 🛠️ Troubleshooting

### Common Issues

**Desktop notifications not working:**
```bash
pip install plyer
```

**Email notifications failing:**
- Check your email credentials in `.env`
- For Gmail, use an App Password instead of your regular password
- Enable 2-factor authentication and generate an App Password

**No station data found:**
- The bot uses multiple fallback methods to find station data
- If Ecobox changes their API, some methods may fail
- Check the logs in `ev_monitor.log` for detailed error information

### Logs

The bot creates detailed logs in `ev_monitor.log` with:
- Station check results
- Notification attempts
- Error messages and debugging information

## 🔧 Advanced Usage

### Running as a Service

**Windows (Task Scheduler):**
1. Open Task Scheduler
2. Create Basic Task
3. Set trigger (e.g., "At startup")
4. Set action to run: `python C:\path\to\your\bot\cli.py start`

**Linux/macOS (systemd/launchd):**
Create a service file to run the bot automatically on system startup.

### Custom Station Configuration

To monitor different stations, modify `config.py`:

```python
stations: List[StationConfig] = Field(default_factory=lambda: [
    StationConfig(name="Your-Station-1"),
    StationConfig(name="Your-Station-2"),
    # Add more stations here
])
```

## 📝 File Structure

```
EVCheck/
├── cli.py                 # Command-line interface
├── monitor.py            # Main monitoring logic
├── station_checker.py    # Station availability checker
├── notifications.py      # Notification system
├── models.py            # Data models
├── config.py            # Configuration management
├── requirements.txt     # Python dependencies
├── .env.example         # Configuration template
├── README.md           # This file
└── run_monitor.py      # Simple runner script
```

## 🤝 Contributing

This bot is designed specifically for Ecobox charging stations in Turkey. If you need to adapt it for other charging networks:

1. Modify `station_checker.py` to work with different APIs
2. Update the station configuration in `config.py`
3. Adjust notification messages as needed

## 📄 License

This project is provided as-is for personal use. Please respect the terms of service of any APIs or websites you interact with.

## ⚠️ Disclaimer

This bot is not affiliated with Ecobox. It's an independent tool created to help EV owners monitor charging station availability. Use responsibly and in accordance with Ecobox's terms of service.
