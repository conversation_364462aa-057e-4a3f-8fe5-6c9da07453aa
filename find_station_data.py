"""Tools to help discover Ecobox station data sources."""

import requests
import json
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from loguru import logger

class StationDataDiscovery:
    """Tools to discover how to access Ecobox station data."""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'tr-TR,tr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
    
    def discover_api_endpoints(self):
        """Try to discover API endpoints by analyzing the website."""
        print("🔍 Discovering API endpoints...")
        
        base_urls = [
            "https://www.ecoboxsarj.com",
            "http://harita.ecoboxsarj.com",
            "https://api.ecoboxsarj.com",
            "https://app.ecoboxsarj.com",
            "https://mobile.ecoboxsarj.com"
        ]
        
        endpoints_to_try = [
            "/api/stations",
            "/api/v1/stations", 
            "/api/v2/stations",
            "/stations",
            "/stations.json",
            "/data/stations",
            "/mobile/api/stations",
            "/map/stations",
            "/charging-stations",
            "/istasyonlar",
            "/sarj-istasyonlari"
        ]
        
        found_endpoints = []
        
        for base_url in base_urls:
            for endpoint in endpoints_to_try:
                url = base_url + endpoint
                try:
                    response = self.session.get(url, timeout=10)
                    if response.status_code == 200:
                        content_type = response.headers.get('content-type', '').lower()
                        
                        if 'json' in content_type:
                            try:
                                data = response.json()
                                print(f"✅ Found JSON endpoint: {url}")
                                print(f"   Data structure: {type(data)}")
                                if isinstance(data, list) and len(data) > 0:
                                    print(f"   Sample item keys: {list(data[0].keys()) if isinstance(data[0], dict) else 'Not dict'}")
                                elif isinstance(data, dict):
                                    print(f"   Keys: {list(data.keys())}")
                                found_endpoints.append((url, data))
                            except:
                                print(f"⚠️  Found endpoint but not JSON: {url}")
                        else:
                            print(f"📄 Found HTML endpoint: {url}")
                            
                except Exception as e:
                    continue
                    
        return found_endpoints
    
    def analyze_website_javascript(self):
        """Analyze the main website for JavaScript that might contain API calls."""
        print("\n🔍 Analyzing website JavaScript...")
        
        try:
            response = self.session.get("https://www.ecoboxsarj.com", timeout=15)
            if response.status_code != 200:
                print("❌ Could not access main website")
                return []
                
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find all script tags
            scripts = soup.find_all('script')
            api_patterns = []
            
            for script in scripts:
                if script.string:
                    content = script.string
                    
                    # Look for API URLs
                    api_urls = re.findall(r'["\']https?://[^"\']*api[^"\']*["\']', content)
                    api_urls.extend(re.findall(r'["\'][^"\']*api[^"\']*["\']', content))
                    
                    # Look for fetch/axios calls
                    fetch_calls = re.findall(r'fetch\(["\']([^"\']+)["\']', content)
                    axios_calls = re.findall(r'axios\.[get|post]+\(["\']([^"\']+)["\']', content)
                    
                    # Look for station-related keywords
                    station_refs = re.findall(r'["\'][^"\']*(?:station|istasyon|sarj)[^"\']*["\']', content, re.IGNORECASE)
                    
                    api_patterns.extend(api_urls + fetch_calls + axios_calls + station_refs)
            
            # Remove duplicates and clean up
            unique_patterns = list(set([p.strip('"\'') for p in api_patterns if p.strip('"\'')]))
            
            print(f"Found {len(unique_patterns)} potential API patterns:")
            for pattern in unique_patterns[:20]:  # Show first 20
                print(f"   {pattern}")
                
            return unique_patterns
            
        except Exception as e:
            print(f"❌ Error analyzing JavaScript: {e}")
            return []
    
    def check_map_data_sources(self):
        """Check the map page for data sources."""
        print("\n🗺️  Checking map data sources...")
        
        map_urls = [
            "http://harita.ecoboxsarj.com",
            "https://www.ecoboxsarj.com/harita",
            "https://www.ecoboxsarj.com/map"
        ]
        
        for url in map_urls:
            try:
                response = self.session.get(url, timeout=15)
                if response.status_code == 200:
                    print(f"✅ Accessed map: {url}")
                    
                    # Look for embedded data or API calls
                    content = response.text
                    
                    # Look for JSON data embedded in the page
                    json_matches = re.findall(r'(\{[^{}]*(?:station|istasyon)[^{}]*\})', content, re.IGNORECASE)
                    for match in json_matches[:5]:  # Show first 5
                        try:
                            data = json.loads(match)
                            print(f"   Found embedded JSON: {data}")
                        except:
                            print(f"   Found potential JSON: {match[:100]}...")
                    
                    # Look for script sources that might load station data
                    soup = BeautifulSoup(content, 'html.parser')
                    scripts = soup.find_all('script', src=True)
                    for script in scripts:
                        src = script.get('src')
                        if any(keyword in src.lower() for keyword in ['station', 'map', 'data', 'api']):
                            full_url = urljoin(url, src)
                            print(f"   Potential data script: {full_url}")
                            
            except Exception as e:
                print(f"❌ Could not access {url}: {e}")
    
    def try_common_mobile_api_patterns(self):
        """Try common mobile app API patterns."""
        print("\n📱 Trying mobile app API patterns...")
        
        # Common mobile API base URLs
        mobile_bases = [
            "https://api.ecoboxsarj.com",
            "https://mobile-api.ecoboxsarj.com", 
            "https://app-api.ecoboxsarj.com",
            "https://backend.ecoboxsarj.com",
            "https://services.ecoboxsarj.com"
        ]
        
        # Common mobile API endpoints
        mobile_endpoints = [
            "/v1/stations",
            "/v2/stations", 
            "/stations/list",
            "/stations/nearby",
            "/charging-points",
            "/locations",
            "/map/points"
        ]
        
        # Try with mobile app headers
        mobile_headers = {
            'User-Agent': 'EcoboxSarj/1.0 (iPhone; iOS 16.0; Scale/3.00)',
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        for base in mobile_bases:
            for endpoint in mobile_endpoints:
                url = base + endpoint
                try:
                    # Try with mobile headers
                    response = self.session.get(url, headers=mobile_headers, timeout=10)
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            print(f"✅ Mobile API found: {url}")
                            print(f"   Response type: {type(data)}")
                            if isinstance(data, list) and data:
                                print(f"   Sample item: {data[0] if len(data) > 0 else 'Empty'}")
                            elif isinstance(data, dict):
                                print(f"   Keys: {list(data.keys())}")
                            return url, data
                        except:
                            print(f"⚠️  Mobile endpoint found but not JSON: {url}")
                except:
                    continue
        
        return None, None
    
    def search_for_hittown_references(self):
        """Search for specific references to HitTown stations."""
        print("\n🎯 Searching for HitTown station references...")
        
        search_terms = ["hittown", "hit town", "HitTown-1", "HitTown-2"]
        
        # Search in main website
        try:
            response = self.session.get("https://www.ecoboxsarj.com", timeout=15)
            if response.status_code == 200:
                content = response.text.lower()
                
                for term in search_terms:
                    if term.lower() in content:
                        print(f"✅ Found '{term}' on main website")
                        
                        # Find context around the term
                        index = content.find(term.lower())
                        context = content[max(0, index-100):index+100]
                        print(f"   Context: ...{context}...")
        except Exception as e:
            print(f"❌ Error searching website: {e}")
        
        # Try Google search for more specific information
        print("\n💡 Suggestion: Try these manual searches:")
        print("   1. Google: 'HitTown Ecobox şarj istasyonu'")
        print("   2. Google: 'site:ecoboxsarj.com HitTown'")
        print("   3. Check Ecobox mobile app directly")

def main():
    """Run station data discovery."""
    print("🔍 EV Station Data Discovery Tool")
    print("=" * 50)
    
    discovery = StationDataDiscovery()
    
    # Try all discovery methods
    endpoints = discovery.discover_api_endpoints()
    js_patterns = discovery.analyze_website_javascript()
    discovery.check_map_data_sources()
    mobile_url, mobile_data = discovery.try_common_mobile_api_patterns()
    discovery.search_for_hittown_references()
    
    print("\n📋 Summary:")
    print(f"   Found {len(endpoints)} potential API endpoints")
    print(f"   Found {len(js_patterns)} JavaScript patterns")
    
    if mobile_url:
        print(f"   Found mobile API: {mobile_url}")
    
    print("\n💡 Next Steps:")
    print("   1. Install a network monitoring tool (like Charles Proxy or Wireshark)")
    print("   2. Use the Ecobox mobile app while monitoring network traffic")
    print("   3. Look for API calls when the app loads station data")
    print("   4. Check the browser's Network tab when visiting harita.ecoboxsarj.com")

if __name__ == "__main__":
    main()
