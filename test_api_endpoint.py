"""Simple script to test discovered API endpoints."""

import requests
import json
import sys
from datetime import datetime

def test_api_endpoint(url, headers=None):
    """Test a discovered API endpoint."""
    
    if headers is None:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'tr-TR,tr;q=0.9,en;q=0.8',
        }
    
    print(f"🔗 Testing API endpoint: {url}")
    print(f"📅 Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)
    
    try:
        response = requests.get(url, headers=headers, timeout=15)
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ SUCCESS!")
            
            # Try to parse as JSON
            try:
                data = response.json()
                print(f"📦 Response Type: {type(data)}")
                
                # Save full response
                filename = f"api_response_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                print(f"💾 Full response saved to: {filename}")
                
                # Analyze the data structure
                print("\n🔍 Data Analysis:")
                if isinstance(data, list):
                    print(f"   📝 Array with {len(data)} items")
                    if len(data) > 0 and isinstance(data[0], dict):
                        print(f"   🔑 Sample item keys: {list(data[0].keys())}")
                        
                        # Look for HitTown stations
                        hittown_stations = []
                        for item in data:
                            if isinstance(item, dict):
                                name = str(item.get('name', '')).lower()
                                if 'hittown' in name:
                                    hittown_stations.append(item)
                        
                        if hittown_stations:
                            print(f"🎯 Found {len(hittown_stations)} HitTown stations!")
                            for station in hittown_stations:
                                print(f"   📍 {station}")
                        else:
                            print("❌ No HitTown stations found in response")
                            
                elif isinstance(data, dict):
                    print(f"   🔑 Object keys: {list(data.keys())}")
                    
                    # Look for nested station data
                    for key, value in data.items():
                        if isinstance(value, list) and len(value) > 0:
                            print(f"   📋 {key}: array with {len(value)} items")
                            if isinstance(value[0], dict):
                                print(f"      🔑 Item keys: {list(value[0].keys())}")
                
                # Show first few items/keys for manual inspection
                print(f"\n📄 Sample Data (first 500 chars):")
                sample = json.dumps(data, indent=2, ensure_ascii=False)[:500]
                print(sample)
                if len(sample) >= 500:
                    print("... (truncated)")
                
            except json.JSONDecodeError:
                print("⚠️  Response is not JSON")
                print(f"📄 Content Type: {response.headers.get('content-type', 'unknown')}")
                print(f"📄 Response (first 500 chars): {response.text[:500]}")
                
        else:
            print(f"❌ FAILED with status {response.status_code}")
            print(f"📄 Response: {response.text[:200]}")
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out")
    except requests.exceptions.ConnectionError:
        print("🔌 Connection error")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def main():
    """Main function to test API endpoints."""
    
    if len(sys.argv) < 2:
        print("🔍 API Endpoint Tester")
        print("=" * 40)
        print("Usage: python test_api_endpoint.py <URL>")
        print("\nExample:")
        print("  python test_api_endpoint.py https://api.ecoboxsarj.com/stations")
        print("\nCommon endpoints to try:")
        print("  https://api.ecoboxsarj.com/v1/stations")
        print("  https://api.ecoboxsarj.com/stations")
        print("  https://backend.ecoboxsarj.com/api/stations")
        print("  https://mobile.ecoboxsarj.com/api/charging-points")
        return
    
    url = sys.argv[1]
    
    # Test with different header combinations
    header_sets = [
        # Standard browser headers
        {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'tr-TR,tr;q=0.9,en;q=0.8',
        },
        
        # Mobile app headers
        {
            'User-Agent': 'EcoboxSarj/1.0 (iPhone; iOS 16.0; Scale/3.00)',
            'Accept': 'application/json',
            'Content-Type': 'application/json',
        },
        
        # API client headers
        {
            'User-Agent': 'EcoboxAPI/1.0',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        }
    ]
    
    for i, headers in enumerate(header_sets, 1):
        print(f"\n🧪 Test {i}/3 - {['Browser', 'Mobile App', 'API Client'][i-1]} Headers")
        test_api_endpoint(url, headers)
        print("\n" + "="*60)

if __name__ == "__main__":
    main()
