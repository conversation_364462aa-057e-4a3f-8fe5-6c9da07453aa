"""Data models for the EV charging station monitor."""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field

class StationStatus(str, Enum):
    """Possible charging station statuses."""
    AVAILABLE = "available"
    OCCUPIED = "occupied"
    OUT_OF_ORDER = "out_of_order"
    UNKNOWN = "unknown"

class ChargingStation(BaseModel):
    """Model representing a charging station."""
    name: str
    station_id: Optional[str] = None
    location: Optional[str] = None
    status: StationStatus = StationStatus.UNKNOWN
    last_updated: datetime = Field(default_factory=datetime.now)
    additional_info: Dict[str, Any] = Field(default_factory=dict)
    
    def is_available(self) -> bool:
        """Check if the station is available for charging."""
        return self.status == StationStatus.AVAILABLE
    
    def status_changed(self, new_status: StationStatus) -> bool:
        """Check if the status has changed from the current status."""
        return self.status != new_status

class MonitoringResult(BaseModel):
    """Result of a monitoring check."""
    timestamp: datetime = Field(default_factory=datetime.now)
    stations: Dict[str, ChargingStation] = Field(default_factory=dict)
    success: bool = True
    error_message: Optional[str] = None
    
class NotificationEvent(BaseModel):
    """Event that triggers a notification."""
    station_name: str
    old_status: StationStatus
    new_status: StationStatus
    timestamp: datetime = Field(default_factory=datetime.now)
    message: str = ""
    
    def __post_init__(self):
        if not self.message:
            self.message = f"Station {self.station_name} changed from {self.old_status.value} to {self.new_status.value}"
