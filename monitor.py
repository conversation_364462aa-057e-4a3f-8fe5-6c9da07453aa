"""Main monitoring system for EV charging stations."""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Optional

from loguru import logger

from models import ChargingStation, StationStatus, NotificationEvent
from station_checker import EcoboxStationChecker
from notifications import NotificationManager
from config import config

class StationMonitor:
    """Main monitoring system for charging stations."""
    
    def __init__(self):
        self.checker = EcoboxStationChecker()
        self.notifier = NotificationManager()
        self.state_file = Path("station_states.json")
        self.previous_states: Dict[str, ChargingStation] = {}
        self.running = False
        
        # Load previous states if available
        self._load_previous_states()
        
    def start_monitoring(self):
        """Start the monitoring loop."""
        logger.info("Starting EV charging station monitor")
        logger.info(f"Monitoring stations: {[station.name for station in config.stations]}")
        logger.info(f"Check interval: {config.monitoring.check_interval_minutes} minutes")
        
        self.running = True
        
        # Send startup notification
        self.notifier.send_startup_notification()
        
        try:
            while self.running:
                self._check_stations()
                self._wait_for_next_check()
                
        except KeyboardInterrupt:
            logger.info("Monitoring stopped by user")
        except Exception as e:
            logger.error(f"Monitoring error: {str(e)}")
        finally:
            self.running = False
            logger.info("Monitoring stopped")
    
    def stop_monitoring(self):
        """Stop the monitoring loop."""
        self.running = False
        
    def _check_stations(self):
        """Check all configured stations."""
        logger.info("Checking station availability...")
        
        station_names = [station.name for station in config.stations]
        result = self.checker.check_all_stations(station_names)
        
        if not result.success:
            logger.error(f"Station check failed: {result.error_message}")
            return
            
        # Process results and check for status changes
        for station_name, current_station in result.stations.items():
            self._process_station_update(station_name, current_station)
            
        # Save current states
        self._save_current_states(result.stations)
        
        logger.info(f"Station check completed. Status: {self._get_status_summary(result.stations)}")
    
    def _process_station_update(self, station_name: str, current_station: ChargingStation):
        """Process a station status update and send notifications if needed."""
        previous_station = self.previous_states.get(station_name)
        
        if previous_station is None:
            # First time seeing this station
            logger.info(f"First check for {station_name}: {current_station.status.value}")
            return
            
        # Check if status changed
        if current_station.status != previous_station.status:
            logger.info(f"Status change for {station_name}: "
                       f"{previous_station.status.value} -> {current_station.status.value}")
            
            # Create notification event
            event = NotificationEvent(
                station_name=station_name,
                old_status=previous_station.status,
                new_status=current_station.status,
                message=f"🔋 {station_name} is now {current_station.status.value.replace('_', ' ').title()}!"
            )
            
            # Send notification if appropriate
            if self.notifier.should_notify(station_name, current_station.status):
                self.notifier.send_notification(event)
    
    def _get_status_summary(self, stations: Dict[str, ChargingStation]) -> str:
        """Get a summary of current station statuses."""
        status_counts = {}
        for station in stations.values():
            status = station.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
            
        return ", ".join([f"{count} {status}" for status, count in status_counts.items()])
    
    def _wait_for_next_check(self):
        """Wait for the next check interval."""
        interval_seconds = config.monitoring.check_interval_minutes * 60
        
        # Sleep in small increments to allow for graceful shutdown
        for _ in range(interval_seconds):
            if not self.running:
                break
            time.sleep(1)
    
    def _load_previous_states(self):
        """Load previous station states from file."""
        if not self.state_file.exists():
            return
            
        try:
            with open(self.state_file, 'r') as f:
                data = json.load(f)
                
            for station_name, station_data in data.items():
                # Convert back to ChargingStation object
                station = ChargingStation(**station_data)
                self.previous_states[station_name] = station
                
            logger.info(f"Loaded previous states for {len(self.previous_states)} stations")
            
        except Exception as e:
            logger.warning(f"Failed to load previous states: {str(e)}")
    
    def _save_current_states(self, stations: Dict[str, ChargingStation]):
        """Save current station states to file."""
        try:
            # Convert ChargingStation objects to dict for JSON serialization
            data = {}
            for station_name, station in stations.items():
                data[station_name] = station.model_dump()
                
            with open(self.state_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
                
            # Update previous states
            self.previous_states = stations.copy()
            
        except Exception as e:
            logger.error(f"Failed to save station states: {str(e)}")
    
    def get_current_status(self) -> Dict[str, ChargingStation]:
        """Get current status of all monitored stations."""
        station_names = [station.name for station in config.stations]
        result = self.checker.check_all_stations(station_names)
        return result.stations
    
    def test_notifications(self):
        """Test the notification system."""
        logger.info("Testing notification system...")
        success = self.notifier.send_test_notification()
        if success:
            logger.info("Test notification sent successfully")
        else:
            logger.error("Test notification failed")
        return success

def main():
    """Main entry point."""
    # Configure logging
    logger.add("ev_monitor.log", rotation="1 day", retention="7 days")
    
    monitor = StationMonitor()
    
    try:
        monitor.start_monitoring()
    except Exception as e:
        logger.error(f"Monitor failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
