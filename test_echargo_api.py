"""Test script specifically for the echargo API discovered in Network tab."""

import requests
import json
from datetime import datetime

def test_echargo_endpoints():
    """Test various echargo API endpoints."""
    
    base_url = "https://map-api.echargo.com"
    
    # Common endpoint patterns to try
    endpoints = [
        "/api/charging-stations",
        "/api/stations",
        "/api/locations", 
        "/api/map/stations",
        "/api/map/charging-points",
        "/api/v1/charging-stations",
        "/api/v1/stations",
        "/api/v1/locations",
        "/api/v2/stations",
        "/api/providers/ecobox/stations",
        "/api/search/stations",
        "/charging-stations",
        "/stations",
        "/locations",
        "/map/data",
        "/map/stations",
    ]
    
    # Headers that might be required
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'tr-TR,tr;q=0.9,en;q=0.8',
        'Referer': 'https://harita.ecoboxsarj.com/',
        'Origin': 'https://harita.ecoboxsarj.com',
    }
    
    print("🔍 Testing echargo API endpoints...")
    print("=" * 60)
    
    successful_endpoints = []
    
    for endpoint in endpoints:
        url = base_url + endpoint
        
        try:
            print(f"\n🔗 Testing: {endpoint}")
            response = requests.get(url, headers=headers, timeout=10)
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ SUCCESS!")
                successful_endpoints.append(url)
                
                try:
                    data = response.json()
                    print(f"   📦 Data type: {type(data)}")
                    
                    # Save response
                    filename = f"echargo_response_{endpoint.replace('/', '_')}.json"
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(data, f, indent=2, ensure_ascii=False)
                    print(f"   💾 Saved to: {filename}")
                    
                    # Look for station data
                    if isinstance(data, list):
                        print(f"   📋 Array with {len(data)} items")
                        if len(data) > 0:
                            print(f"   🔍 Sample item: {str(data[0])[:100]}...")
                    elif isinstance(data, dict):
                        print(f"   🔑 Keys: {list(data.keys())}")
                        
                except json.JSONDecodeError:
                    print(f"   ⚠️  Not JSON: {response.text[:100]}...")
                    
            elif response.status_code == 404:
                print("   ❌ Not found")
            elif response.status_code == 403:
                print("   🔒 Forbidden - might need authentication")
            elif response.status_code == 401:
                print("   🔑 Unauthorized - needs authentication")
            else:
                print(f"   ⚠️  Status {response.status_code}: {response.text[:100]}...")
                
        except requests.exceptions.Timeout:
            print("   ⏰ Timeout")
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    print(f"\n📊 Summary: Found {len(successful_endpoints)} working endpoints")
    for url in successful_endpoints:
        print(f"   ✅ {url}")
    
    return successful_endpoints

def test_with_parameters():
    """Test endpoints with common query parameters."""
    
    base_url = "https://map-api.echargo.com/api"
    
    # Ankara coordinates (approximate)
    ankara_lat = 39.9334
    ankara_lng = 32.8597
    
    # Common parameter patterns
    param_sets = [
        {"lat": ankara_lat, "lng": ankara_lng},
        {"lat": ankara_lat, "lng": ankara_lng, "radius": 50},
        {"lat": ankara_lat, "lng": ankara_lng, "zoom": 12},
        {"region": "ankara"},
        {"city": "ankara"},
        {"provider": "ecobox"},
        {"brand": "ecobox"},
        {"search": "hittown"},
        {"q": "hittown"},
        {"name": "hittown"},
    ]
    
    endpoints = ["/stations", "/charging-stations", "/locations", "/search"]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json',
        'Referer': 'https://harita.ecoboxsarj.com/',
    }
    
    print("\n🎯 Testing with parameters...")
    print("=" * 60)
    
    for endpoint in endpoints:
        for params in param_sets:
            url = base_url + endpoint
            
            try:
                response = requests.get(url, headers=headers, params=params, timeout=10)
                
                if response.status_code == 200:
                    print(f"✅ SUCCESS: {endpoint} with {params}")
                    
                    try:
                        data = response.json()
                        filename = f"echargo_params_{endpoint.replace('/', '_')}_{len(params)}.json"
                        with open(filename, 'w', encoding='utf-8') as f:
                            json.dump(data, f, indent=2, ensure_ascii=False)
                        print(f"   💾 Saved to: {filename}")
                        
                        # Quick analysis
                        if isinstance(data, list) and len(data) > 0:
                            print(f"   📋 Found {len(data)} items")
                        elif isinstance(data, dict):
                            print(f"   🔑 Keys: {list(data.keys())}")
                            
                    except:
                        pass
                        
            except:
                continue

def main():
    """Main function."""
    print("🚀 echargo API Discovery")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Test basic endpoints
    successful = test_echargo_endpoints()
    
    # Test with parameters
    test_with_parameters()
    
    print("\n💡 Next Steps:")
    print("1. Check the saved JSON files for station data")
    print("2. Look for HitTown stations in the responses")
    print("3. Note the data structure for integration")
    print("4. Check the Network tab for the exact URL used by the map")

if __name__ == "__main__":
    main()
