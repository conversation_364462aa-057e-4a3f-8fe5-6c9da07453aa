# 🔍 How to Find Ecobox Station Data

The bot currently shows "Unknown" status because we need to discover how Ecobox exposes their station data. Here are the most effective methods:

## 🎯 Method 1: Mobile App Network Monitoring (RECOMMENDED)

This is the most reliable way to find the API endpoints:

### Step 1: Install Network Monitoring
**For Android:**
- Install [HTTP Catcher](https://play.google.com/store/apps/details?id=com.charlesproxy.httphelper) (Free)
- Or [<PERSON> Proxy](https://www.charlesproxy.com/) (Paid)

**For iPhone:**
- Install [<PERSON> Proxy](https://apps.apple.com/app/charles-proxy/id1134218562) 
- Or use [Proxyman](https://proxyman.io/)

### Step 2: Setup Monitoring
1. Open HTTP Catcher (or your chosen tool)
2. Start capturing network traffic
3. Open the Ecobox mobile app
4. Navigate to the map/station list

### Step 3: Find API Calls
Look for HTTP requests to URLs containing:
- `api.ecoboxsarj.com`
- `backend.ecoboxsarj.com` 
- `/stations`
- `/charging-points`
- `/locations`

### Step 4: Analyze the Response
When you find a request that returns station data:
1. Note the URL (e.g., `https://api.ecoboxsarj.com/v1/stations`)
2. Check the request headers
3. Look at the JSON response structure
4. Find HitTown-1 and HitTown-2 in the data

## 🌐 Method 2: Browser Developer Tools

### Step 1: Open Browser Tools
1. Open Chrome or Firefox
2. Go to `http://harita.ecoboxsarj.com`
3. Press F12 to open Developer Tools
4. Click the "Network" tab

### Step 2: Monitor Requests
1. Refresh the page
2. Look for XHR/Fetch requests
3. Check each request for station data
4. Look for JSON responses containing station information

### Step 3: Test the APIs
When you find an API endpoint:
```bash
curl "https://discovered-api-url.com/stations" -H "User-Agent: Mozilla/5.0..."
```

## 🔧 Method 3: Update the Bot

Once you find the API endpoint, update `station_checker.py`:

### Example: If you find an API at `https://api.ecoboxsarj.com/v1/stations`

```python
def _check_via_discovered_api(self, station: ChargingStation) -> Optional[ChargingStation]:
    """Check station via discovered API."""
    try:
        # Use the discovered API endpoint
        api_url = "https://api.ecoboxsarj.com/v1/stations"  # Replace with actual URL
        
        headers = {
            'User-Agent': self.session.headers['User-Agent'],
            # Add any required headers you discovered
        }
        
        response = self.session.get(api_url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            # Find your station in the response
            for station_data in data:  # Adjust based on actual response structure
                station_name = station_data.get('name', '').lower()
                
                if station.name.lower() in station_name:
                    # Parse the status
                    status_field = station_data.get('status', 'unknown')  # Adjust field name
                    
                    if status_field == 'available':  # Adjust based on actual values
                        station.status = StationStatus.AVAILABLE
                    elif status_field == 'occupied':
                        station.status = StationStatus.OCCUPIED
                    else:
                        station.status = StationStatus.UNKNOWN
                    
                    station.additional_info = {
                        'source': 'discovered_api',
                        'raw_data': station_data
                    }
                    return station
                    
    except Exception as e:
        logger.debug(f"Discovered API check failed: {str(e)}")
        
    return None
```

Then add this method to the checking chain in `check_station_availability`:

```python
station = self._check_via_discovered_api(station) or \
         self._check_via_map_api(station) or \
         self._check_via_website_scraping(station) or \
         self._check_via_mobile_api(station) or \
         station
```

## 🎯 What to Look For

### API Response Examples
Look for JSON responses like:
```json
{
  "stations": [
    {
      "id": "123",
      "name": "HitTown-1",
      "status": "available",
      "location": {
        "lat": 39.9334,
        "lng": 32.8597
      }
    }
  ]
}
```

### Common Status Values
- `available`, `free`, `open` = Available
- `occupied`, `busy`, `charging` = Occupied  
- `offline`, `error`, `maintenance` = Out of Order

## 🚀 Quick Test

Once you find an API endpoint, test it quickly:

```bash
# Test the endpoint
curl "YOUR_DISCOVERED_API_URL" -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

# Or use Python
python -c "
import requests
response = requests.get('YOUR_API_URL')
print(response.json())
"
```

## 📱 Alternative: Contact Ecobox

If technical methods don't work:
1. Contact Ecobox support: `<EMAIL>`
2. Ask if they have a public API for station status
3. Mention you're building a monitoring tool for personal use

## 🔄 Update the Bot

After finding the API:
1. Update `station_checker.py` with the new method
2. Test with: `python cli.py check`
3. Start monitoring: `python cli.py start`

## 💡 Tips

- **Mobile apps usually have the most reliable APIs**
- **Look for requests that happen when the map loads**
- **Station data is often in `/stations`, `/locations`, or `/charging-points` endpoints**
- **The API might require specific headers or authentication**
- **Some APIs return all stations, others allow filtering by location**

## 🆘 Need Help?

If you find an API endpoint but need help integrating it:
1. Share the URL and sample response
2. Note any required headers
3. I can help update the bot code

The bot is designed to be easily updated once we discover the data source!
