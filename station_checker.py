"""Station availability checker for Ecobox charging stations."""

import requests
import time
from datetime import datetime
from typing import Dict, List, Optional
from bs4 import BeautifulSoup
from loguru import logger

from models import ChargingStation, StationStatus, MonitoringResult
from config import config

class EcoboxStationChecker:
    """Checker for Ecobox charging station availability."""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': config.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'tr-TR,tr;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
    def check_station_availability(self, station_name: str) -> ChargingStation:
        """Check availability of a specific station."""
        logger.info(f"Checking availability for station: {station_name}")
        
        station = ChargingStation(
            name=station_name,
            status=StationStatus.UNKNOWN,
            last_updated=datetime.now()
        )
        
        try:
            # Try multiple approaches to get station data
            station = self._check_via_map_api(station) or \
                     self._check_via_website_scraping(station) or \
                     self._check_via_mobile_api(station) or \
                     station
                     
        except Exception as e:
            logger.error(f"Error checking station {station_name}: {str(e)}")
            station.additional_info["error"] = str(e)
            
        return station
    
    def _check_via_map_api(self, station: ChargingStation) -> Optional[ChargingStation]:
        """Try to get station data via map API."""
        try:
            # Try to access the map data
            map_url = config.ecobox_map_url
            response = self.session.get(map_url, timeout=config.monitoring.timeout_seconds)
            
            if response.status_code == 200:
                # Look for station data in the response
                content = response.text.lower()
                station_name_lower = station.name.lower()
                
                # Simple heuristic: if we find the station name, try to determine status
                if station_name_lower in content:
                    logger.info(f"Found station {station.name} in map data")
                    # This is a placeholder - would need to parse actual API response
                    station.status = StationStatus.AVAILABLE  # Default assumption
                    station.additional_info["source"] = "map_api"
                    return station
                    
        except Exception as e:
            logger.debug(f"Map API check failed: {str(e)}")
            
        return None
    
    def _check_via_website_scraping(self, station: ChargingStation) -> Optional[ChargingStation]:
        """Try to get station data via website scraping."""
        try:
            # Check the main website for station information
            response = self.session.get(config.ecobox_base_url, timeout=config.monitoring.timeout_seconds)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Look for station references in the page
                page_text = soup.get_text().lower()
                station_name_lower = station.name.lower()
                
                if station_name_lower in page_text:
                    logger.info(f"Found station {station.name} mentioned on website")
                    # This is a placeholder - would need more sophisticated parsing
                    station.status = StationStatus.AVAILABLE  # Default assumption
                    station.additional_info["source"] = "website_scraping"
                    return station
                    
        except Exception as e:
            logger.debug(f"Website scraping failed: {str(e)}")
            
        return None
    
    def _check_via_mobile_api(self, station: ChargingStation) -> Optional[ChargingStation]:
        """Try to get station data via mobile app API endpoints."""
        try:
            # Common mobile API endpoints to try
            api_endpoints = [
                f"{config.ecobox_base_url}/api/stations",
                f"{config.ecobox_base_url}/api/v1/stations",
                f"{config.ecobox_base_url}/mobile/api/stations",
                "https://api.ecoboxsarj.com/stations",
                "https://api.ecoboxsarj.com/v1/stations",
            ]
            
            for endpoint in api_endpoints:
                try:
                    response = self.session.get(endpoint, timeout=config.monitoring.timeout_seconds)
                    
                    if response.status_code == 200:
                        # Try to parse JSON response
                        try:
                            data = response.json()
                            logger.info(f"Got API response from {endpoint}")
                            
                            # Look for our station in the response
                            station_data = self._find_station_in_api_response(data, station.name)
                            if station_data:
                                station.status = self._parse_station_status(station_data)
                                station.additional_info.update({
                                    "source": "mobile_api",
                                    "endpoint": endpoint,
                                    "raw_data": station_data
                                })
                                return station
                                
                        except ValueError:
                            # Not JSON, might be HTML or other format
                            continue
                            
                except Exception as e:
                    logger.debug(f"API endpoint {endpoint} failed: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.debug(f"Mobile API check failed: {str(e)}")
            
        return None
    
    def _find_station_in_api_response(self, data: dict, station_name: str) -> Optional[dict]:
        """Find station data in API response."""
        station_name_lower = station_name.lower()
        
        # Handle different possible API response structures
        if isinstance(data, list):
            for item in data:
                if isinstance(item, dict):
                    name = item.get('name', '').lower()
                    if station_name_lower in name or name in station_name_lower:
                        return item
        elif isinstance(data, dict):
            # Check if data contains stations array
            stations = data.get('stations', data.get('data', []))
            if isinstance(stations, list):
                return self._find_station_in_api_response(stations, station_name)
            
            # Check if this is a single station object
            name = data.get('name', '').lower()
            if station_name_lower in name or name in station_name_lower:
                return data
                
        return None
    
    def _parse_station_status(self, station_data: dict) -> StationStatus:
        """Parse station status from API data."""
        # Common status field names to check
        status_fields = ['status', 'state', 'availability', 'available', 'occupied']
        
        for field in status_fields:
            if field in station_data:
                value = str(station_data[field]).lower()
                
                if value in ['available', 'free', 'open', 'ready']:
                    return StationStatus.AVAILABLE
                elif value in ['occupied', 'busy', 'charging', 'in_use']:
                    return StationStatus.OCCUPIED
                elif value in ['out_of_order', 'offline', 'error', 'maintenance']:
                    return StationStatus.OUT_OF_ORDER
                    
        # Default to unknown if we can't determine status
        return StationStatus.UNKNOWN
    
    def check_all_stations(self, station_names: List[str]) -> MonitoringResult:
        """Check availability of all specified stations."""
        logger.info(f"Checking {len(station_names)} stations")
        
        result = MonitoringResult()
        
        try:
            for station_name in station_names:
                station = self.check_station_availability(station_name)
                result.stations[station_name] = station
                
                # Add small delay between requests to be respectful
                time.sleep(1)
                
        except Exception as e:
            logger.error(f"Error during station checking: {str(e)}")
            result.success = False
            result.error_message = str(e)
            
        return result
