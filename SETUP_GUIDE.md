# 🚀 Setup Guide for EV Charging Station Monitor

This guide will help you set up and configure the EV charging station monitor for HitTown-1 and HitTown-2 Ecobox stations.

## 📋 Prerequisites

- Python 3.8 or higher
- Internet connection
- Windows, macOS, or Linux

## 🔧 Step-by-Step Setup

### 1. Install Dependencies

Open a terminal/command prompt in the project directory and run:

```bash
python cli.py install
```

Or manually install:
```bash
pip install -r requirements.txt
```

### 2. Initial Configuration

Run the setup command:
```bash
python cli.py setup
```

This creates a `.env` file with default settings.

### 3. Configure Notifications (Optional)

#### Desktop Notifications
Desktop notifications are enabled by default and should work immediately.

#### Email Notifications (Optional)
To enable email notifications, edit the `.env` file:

```env
EMAIL_ENABLED=true
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>
EMAIL_PASSWORD=your-app-password
```

**For Gmail users:**
1. Enable 2-factor authentication on your Google account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a password for "Mail"
   - Use this password in the `EMAIL_PASSWORD` field

### 4. Test the System

Test notifications:
```bash
python cli.py test
```

Check current station status:
```bash
python cli.py check
```

View configuration:
```bash
python cli.py config
```

### 5. Start Monitoring

Start continuous monitoring:
```bash
python cli.py start
```

The bot will:
- Check station availability every 5 minutes (configurable)
- Send notifications when stations become available
- Log all activity to `ev_monitor.log`
- Save station states to `station_states.json`

## ⚙️ Configuration Options

Edit the `.env` file to customize:

```env
# Monitoring frequency (1-60 minutes)
CHECK_INTERVAL_MINUTES=5

# Network settings
MAX_RETRIES=3
TIMEOUT_SECONDS=30

# Notifications
DESKTOP_NOTIFICATIONS=true
SOUND_NOTIFICATIONS=true

# Email settings (if enabled)
EMAIL_ENABLED=false
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
```

## 🔄 Running Automatically

### Windows - Task Scheduler

1. Open Task Scheduler
2. Create Basic Task
3. Name: "EV Station Monitor"
4. Trigger: "When the computer starts"
5. Action: "Start a program"
6. Program: `python`
7. Arguments: `C:\path\to\your\project\cli.py start`
8. Start in: `C:\path\to\your\project`

### macOS - launchd

Create `~/Library/LaunchAgents/com.evmonitor.plist`:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.evmonitor</string>
    <key>ProgramArguments</key>
    <array>
        <string>python3</string>
        <string>/path/to/your/project/cli.py</string>
        <string>start</string>
    </array>
    <key>WorkingDirectory</key>
    <string>/path/to/your/project</string>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
</dict>
</plist>
```

Load with: `launchctl load ~/Library/LaunchAgents/com.evmonitor.plist`

### Linux - systemd

Create `/etc/systemd/system/evmonitor.service`:

```ini
[Unit]
Description=EV Charging Station Monitor
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/your/project
ExecStart=/usr/bin/python3 /path/to/your/project/cli.py start
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable with:
```bash
sudo systemctl enable evmonitor.service
sudo systemctl start evmonitor.service
```

## 🔍 Monitoring and Logs

### Log Files
- `ev_monitor.log`: Detailed application logs
- `station_states.json`: Current station states

### Checking Status
```bash
# View current configuration
python cli.py config

# Check station status once
python cli.py check

# Test notifications
python cli.py test
```

## 🛠️ Troubleshooting

### Common Issues

**"Module not found" errors:**
```bash
pip install -r requirements.txt
```

**Desktop notifications not working:**
- On Linux: Install `libnotify-bin`
- On Windows: Should work by default
- On macOS: Should work by default

**Email notifications failing:**
- Check email credentials
- For Gmail, use App Password (not regular password)
- Verify SMTP settings

**No station data:**
- This is normal initially - the bot tries multiple methods to find station data
- Check logs for detailed information
- The system will adapt as Ecobox's API structure is discovered

### Getting Help

1. Check the logs in `ev_monitor.log`
2. Run with verbose logging: `python cli.py -v check`
3. Verify configuration: `python cli.py config`

## 🎯 What to Expect

### Initial Run
- Stations may show as "Unknown" status initially
- The bot will attempt multiple methods to find station data
- Desktop notifications should work immediately

### Ongoing Monitoring
- Checks every 5 minutes by default
- Only notifies when stations become available
- Avoids duplicate notifications (10-minute cooldown)
- Maintains state between restarts

### Notifications
You'll receive notifications when:
- ✅ HitTown-1 becomes available
- ✅ HitTown-2 becomes available
- 🤖 Bot starts monitoring (startup notification)

You won't be notified when:
- Stations become occupied (to avoid spam)
- Status doesn't change
- Less than 10 minutes since last notification

## 🔄 Updates and Maintenance

The bot is designed to be self-maintaining:
- Logs rotate daily (keeps 7 days)
- State persistence prevents duplicate notifications
- Multiple fallback methods for data collection
- Graceful error handling and recovery

To update the monitoring frequency or other settings, simply edit the `.env` file and restart the bot.
