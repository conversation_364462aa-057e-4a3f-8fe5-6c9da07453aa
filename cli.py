"""Command-line interface for the EV charging station monitor."""

import argparse
import sys
from pathlib import Path

from loguru import logger

from monitor import StationMonitor
from config import config
from models import StationStatus

def setup_logging(verbose: bool = False):
    """Setup logging configuration."""
    logger.remove()  # Remove default handler
    
    if verbose:
        logger.add(sys.stderr, level="DEBUG")
    else:
        logger.add(sys.stderr, level="INFO")
        
    logger.add("ev_monitor.log", rotation="1 day", retention="7 days", level="DEBUG")

def cmd_start(args):
    """Start monitoring stations."""
    setup_logging(args.verbose)
    
    logger.info("Starting EV charging station monitor")
    monitor = StationMonitor()
    monitor.start_monitoring()

def cmd_check(args):
    """Check current station status once."""
    setup_logging(args.verbose)
    
    monitor = StationMonitor()
    stations = monitor.get_current_status()
    
    print("\n🔋 Current Station Status:")
    print("=" * 50)
    
    for station_name, station in stations.items():
        status_emoji = {
            StationStatus.AVAILABLE: "✅",
            StationStatus.OCCUPIED: "🔴", 
            StationStatus.OUT_OF_ORDER: "⚠️",
            StationStatus.UNKNOWN: "❓"
        }.get(station.status, "❓")
        
        print(f"{status_emoji} {station_name}: {station.status.value.replace('_', ' ').title()}")
        
        if station.additional_info:
            source = station.additional_info.get('source', 'unknown')
            print(f"   Source: {source}")
            
        print(f"   Last updated: {station.last_updated.strftime('%Y-%m-%d %H:%M:%S')}")
        print()

def cmd_test(args):
    """Test the notification system."""
    setup_logging(args.verbose)
    
    monitor = StationMonitor()
    success = monitor.test_notifications()
    
    if success:
        print("✅ Test notification sent successfully!")
    else:
        print("❌ Test notification failed. Check your configuration.")
        sys.exit(1)

def cmd_config(args):
    """Show current configuration."""
    print("\n⚙️ Current Configuration:")
    print("=" * 50)
    
    print(f"Stations to monitor:")
    for station in config.stations:
        print(f"  - {station.name}")
    
    print(f"\nMonitoring settings:")
    print(f"  Check interval: {config.monitoring.check_interval_minutes} minutes")
    print(f"  Max retries: {config.monitoring.max_retries}")
    print(f"  Timeout: {config.monitoring.timeout_seconds} seconds")
    
    print(f"\nNotifications:")
    print(f"  Desktop notifications: {'✅' if config.notifications.desktop_enabled else '❌'}")
    print(f"  Email notifications: {'✅' if config.notifications.email_enabled else '❌'}")
    
    if config.notifications.email_enabled:
        print(f"  Email from: {config.notifications.email_from}")
        print(f"  Email to: {config.notifications.email_to}")
        print(f"  SMTP server: {config.notifications.email_smtp_server}:{config.notifications.email_smtp_port}")

def cmd_install(args):
    """Install dependencies."""
    import subprocess
    
    print("📦 Installing dependencies...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        sys.exit(1)

def cmd_setup(args):
    """Setup configuration."""
    print("🔧 Setting up EV Station Monitor")
    print("=" * 50)
    
    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        print("Creating .env file from template...")
        
        # Copy .env.example to .env
        example_file = Path(".env.example")
        if example_file.exists():
            env_file.write_text(example_file.read_text())
            print("✅ Created .env file")
        else:
            print("❌ .env.example not found")
            
    print(f"\n📝 Please edit {env_file.absolute()} to configure:")
    print("  - Email settings (if you want email notifications)")
    print("  - Monitoring intervals")
    print("  - Other preferences")
    
    print(f"\n🚀 After configuration, run:")
    print(f"  python cli.py test    # Test notifications")
    print(f"  python cli.py check   # Check current status")
    print(f"  python cli.py start   # Start monitoring")

def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="EV Charging Station Monitor for Ecobox stations",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python cli.py setup           # Initial setup
  python cli.py start           # Start monitoring
  python cli.py check           # Check current status
  python cli.py test            # Test notifications
  python cli.py config          # Show configuration
        """
    )
    
    parser.add_argument("-v", "--verbose", action="store_true", 
                       help="Enable verbose logging")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Setup command
    setup_parser = subparsers.add_parser("setup", help="Setup configuration")
    setup_parser.set_defaults(func=cmd_setup)
    
    # Install command
    install_parser = subparsers.add_parser("install", help="Install dependencies")
    install_parser.set_defaults(func=cmd_install)
    
    # Start command
    start_parser = subparsers.add_parser("start", help="Start monitoring stations")
    start_parser.set_defaults(func=cmd_start)
    
    # Check command
    check_parser = subparsers.add_parser("check", help="Check current station status")
    check_parser.set_defaults(func=cmd_check)
    
    # Test command
    test_parser = subparsers.add_parser("test", help="Test notification system")
    test_parser.set_defaults(func=cmd_test)
    
    # Config command
    config_parser = subparsers.add_parser("config", help="Show current configuration")
    config_parser.set_defaults(func=cmd_config)
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
        
    try:
        args.func(args)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        logger.error(f"Command failed: {str(e)}")
        if args.verbose:
            raise
        sys.exit(1)

if __name__ == "__main__":
    main()
