"""Configuration settings for the EV charging station monitor bot."""

import os
from typing import List, Dict, Any
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class StationConfig(BaseModel):
    """Configuration for a charging station to monitor."""
    name: str
    location: str = ""
    station_id: str = ""
    
class NotificationConfig(BaseModel):
    """Configuration for notifications."""
    email_enabled: bool = Field(default=False)
    email_smtp_server: str = Field(default="smtp.gmail.com")
    email_smtp_port: int = Field(default=587)
    email_from: str = Field(default="")
    email_to: str = Field(default="")
    email_password: str = Field(default="")
    
    desktop_enabled: bool = Field(default=True)
    sound_enabled: bool = Field(default=True)

class MonitorConfig(BaseModel):
    """Configuration for monitoring behavior."""
    check_interval_minutes: int = Field(default=5, ge=1, le=60)
    max_retries: int = Field(default=3)
    timeout_seconds: int = Field(default=30)
    
class Config(BaseModel):
    """Main configuration class."""
    stations: List[StationConfig] = Field(default_factory=lambda: [
        StationConfig(name="HitTown-1"),
        StationConfig(name="HitTown-2")
    ])
    
    notifications: NotificationConfig = Field(default_factory=NotificationConfig)
    monitoring: MonitorConfig = Field(default_factory=MonitorConfig)
    
    # Ecobox specific settings
    ecobox_base_url: str = Field(default="https://www.ecoboxsarj.com")
    ecobox_map_url: str = Field(default="http://harita.ecoboxsarj.com")
    
    # User agent for web requests
    user_agent: str = Field(default="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

# Global config instance
config = Config()

def load_config_from_env():
    """Load configuration from environment variables."""
    global config
    
    # Email configuration
    if os.getenv("EMAIL_ENABLED", "").lower() == "true":
        config.notifications.email_enabled = True
        config.notifications.email_from = os.getenv("EMAIL_FROM", "")
        config.notifications.email_to = os.getenv("EMAIL_TO", "")
        config.notifications.email_password = os.getenv("EMAIL_PASSWORD", "")
        config.notifications.email_smtp_server = os.getenv("EMAIL_SMTP_SERVER", "smtp.gmail.com")
        config.notifications.email_smtp_port = int(os.getenv("EMAIL_SMTP_PORT", "587"))
    
    # Monitoring configuration
    config.monitoring.check_interval_minutes = int(os.getenv("CHECK_INTERVAL_MINUTES", "5"))
    config.monitoring.max_retries = int(os.getenv("MAX_RETRIES", "3"))
    config.monitoring.timeout_seconds = int(os.getenv("TIMEOUT_SECONDS", "30"))
    
    return config

# Load configuration on import
load_config_from_env()
